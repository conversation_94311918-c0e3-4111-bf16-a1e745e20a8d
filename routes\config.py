from fastapi import APIRouter, Request, Response
from ratelimit import limiter
from utils.config_utils import (
    get_config_from_request,
    get_missing_fields_to_look,
    get_fields_mapping_prompt,
    get_missing_fields_labels,
    get_high_priority_fields,
)


config_router = APIRouter()


def get_field_mappings_xml(request: Request):
    """Generate the FieldMappings XML section based on client configuration."""
    field_mappings = []

    # Get client-specific configuration
    missing_fields = get_missing_fields_to_look(request)
    mapping_prompts = get_fields_mapping_prompt(request)
    field_labels = get_missing_fields_labels(request)
    high_priority = get_high_priority_fields(request)

    for column, prompt, label in zip(missing_fields, mapping_prompts, field_labels):
        is_high_priority = column in high_priority
        field_mappings.append(
            f"""
            <Field>
                <Column>{column}</Column>
                <Prompt>{prompt}</Prompt>
                <Label>{label}</Label>
                <HighPriority>{'true' if is_high_priority else 'false'}</HighPriority>
            </Field>"""
        )

    return "\n".join(field_mappings)


@config_router.get("/v1/config", tags=["Configuration"])
@limiter.limit("5/minute")
async def get_client_config(request: Request):
    """
    Returns the referralAIClientConfig XML with current configuration values.

    This endpoint provides the complete configuration for the Referral AI system
    including authentication, database, workflow agents, and scheduler settings.
    """
    # Get client-specific configuration
    config = get_config_from_request(request)
    field_mappings_xml = get_field_mappings_xml(request)

    xml_content = f"""
<referralAIClientConfig>
  <AIConfiguration>

    <RootPath>{config.root_path}</RootPath>
    <ServerName>{config.server_name}</ServerName>

    <Authentication>
      <JwtSecretKey></JwtSecretKey>
      <EncryptionAlgorithm>{config.encryption_algorithm}</EncryptionAlgorithm>
      <AccessTokenExpireMinutes>{config.access_token_expire_minutes}</AccessTokenExpireMinutes>
      <RefreshTokenExpireMinutes>{config.refresh_token_expire_minutes}</RefreshTokenExpireMinutes>
      <AuthCredentials></AuthCredentials>
      <AuthKeysDelimiter></AuthKeysDelimiter>
      <SwaggerBasicAuthKey></SwaggerBasicAuthKey>
    </Authentication>

    <Database>
      <Host></Host>
      <Port></Port>
      <Name></Name>
      <User></User>
      <Password></Password>
      <PoolSize>{config.db_pool_size}</PoolSize>
      <MaxOverflowConnections>{config.max_overflow_connections}</MaxOverflowConnections>
      <PoolTimeout>{config.pool_timeout}</PoolTimeout>
    </Database>

    <Workers>
      <ClientWorkers>{config.client_workers}</ClientWorkers>
      <ReferralProcessingWorkers>{config.referral_processing_workers}</ReferralProcessingWorkers>
    </Workers>

    <PDFPlus>
      <BaseUrl>{config.pdfplus_url}</BaseUrl>
      <ExtractFieldsEndpoint>{config.pdfplus_extract_fields_endpoint}</ExtractFieldsEndpoint>
      <AuthKey></AuthKey>
    </PDFPlus>

    <WorkflowConfigurations>
      <MissingInfoAgent>
        <DisableAgent>{config.disable_missing_info_agent}</DisableAgent>
        <DisableNewReferralWorkflow>{config.disable_missing_info_new_referral_workflow}</DisableNewReferralWorkflow>
        <DisableRetriggerWorkflow>{config.disable_missing_info_retrigger_workflow}</DisableRetriggerWorkflow>
        <FieldMappings>
{field_mappings_xml}
        </FieldMappings>
      </MissingInfoAgent>

      <TaskWorkflowAgent>
        <DisableAgent>{config.disable_task_and_workflow_agent}</DisableAgent>
        <DisableTaskCreationWorkflow>{config.disable_task_creation_workflow}</DisableTaskCreationWorkflow>
        <TaskSubject>{config.task_subject}</TaskSubject>
        <Status>{config.new_comment_task_status_for_missing_fields}</Status>
        <Comment>{config.comment}</Comment>
        <CommentType>{config.comment_type}</CommentType>
        <CreatedByUsername>{config.created_by_username}</CreatedByUsername>
        <CreatedByUserId>{config.created_by_user_id}</CreatedByUserId>
        <TaskCreationWaitingMinutes>{config.task_creation_waiting_minutes}</TaskCreationWaitingMinutes>
      </TaskWorkflowAgent>

      <PrioritizationAgent>
        <DisableAgent>{config.disable_prioritization_agent}</DisableAgent>
        <DisableAutoDeclineWorkflow>{config.disable_auto_decline_workflow}</DisableAutoDeclineWorkflow>
        <DisablePatientToFacilityDistanceDecline>{config.disable_patient_to_facility_distance_decline}</DisablePatientToFacilityDistanceDecline>
        <DisableSourceToFacilityDistanceDecline>{config.disable_source_to_facility_distance_decline}</DisableSourceToFacilityDistanceDecline>
        <DisableShotgunScoreDecline>{config.disable_shotgun_score_decline}</DisableShotgunScoreDecline>
        <DisableReferralPayerDecline>{config.disable_referral_payer_decline}</DisableReferralPayerDecline>
        <DisableReferralSourceDecline>{config.disable_referral_source_decline}</DisableReferralSourceDecline>
        <DisableReferralTotalScoreDecline>{config.disable_referral_total_score_decline}</DisableReferralTotalScoreDecline>
        <AutoDeclineCriteria>
            <ReferralScores>
                <SourceToFacilityDistanceMiles>{config.source_to_facility_distance_threshold_miles}</SourceToFacilityDistanceMiles>
                <PatientToFacilityDistanceMiles>{config.patient_to_facility_distance_threshold_miles}</PatientToFacilityDistanceMiles>
                <ShotgunScore>{config.shotgun_score_threshold}</ShotgunScore>
                <ReferralPayerScore>{config.referral_payer_score_threshold}</ReferralPayerScore>
                <ReferralSourceScore>{config.referral_source_score_threshold}</ReferralSourceScore>
                <ReferralTotalScore>{config.referral_total_score_threshold}</ReferralTotalScore>
            </ReferralScores>
        </AutoDeclineCriteria>
      </PrioritizationAgent>

      <ErrorMonitoring>
        <DisableMonitoring>{config.disable_error_monitoring}</DisableMonitoring>
        <AlertIntervalMinutes>{config.error_alert_interval_minutes}</AlertIntervalMinutes>
        <AlertSubject>{config.error_alert_subject}</AlertSubject>
        <Email>
            <AuthUser></AuthUser>
            <AppPassword></AppPassword>
            <ToUser></ToUser>
            <FromUser></FromUser>
            <EnablePostfix></EnablePostfix>
        </Email>
      </ErrorMonitoring>
    </WorkflowConfigurations>

    <Scheduler>
      <DisableInternalScheduler>{config.disable_internal_scheduler}</DisableInternalScheduler>
      <ErrorMonitoringIntervalSeconds>{config.error_monitoring_interval_seconds}</ErrorMonitoringIntervalSeconds>
      <MissingInfoAgentIntervalSeconds>{config.missing_info_agent_interval_seconds}</MissingInfoAgentIntervalSeconds>
      <TaskWorkflowAgentIntervalSeconds>{config.task_workflow_agent_interval_seconds}</TaskWorkflowAgentIntervalSeconds>
      <PrioritizationAgentIntervalSeconds>{config.prioritization_agent_interval_seconds}</PrioritizationAgentIntervalSeconds>
    </Scheduler>

  </AIConfiguration>
</referralAIClientConfig>"""

    return Response(
        content=xml_content,
        media_type="application/xml",
    )
