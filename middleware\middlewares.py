from fastapi import Request
import time
import logging
from typing import Callable
from fastapi import Response
import uuid
from utils.logging_config import REQUEST_ID_VAR
from middleware.client_identification import get_client_identification_service
from services.client_config_service import get_client_config_service
from models.client_config import ClientConfiguration


async def http_middleware(request: Request, call_next: Callable) -> Response:
    """
    The HTTP middleware function.
    Args:
        request (Request): The request object.
        call_next (Callable): The next middleware function.
    Returns:
        Response: The response object.
    """
    REQUEST_ID_VAR.set(str(uuid.uuid4()))

    start_time = time.time()

    client_host = request.client.host
    url_path = request.url.path
    logging.info(f"started: {client_host} | {request.method} | {url_path}")

    # Client identification and configuration loading
    await load_client_configuration(request)

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


async def load_client_configuration(request: Request) -> None:
    """
    Load client-specific configuration and store it in request state.

    Args:
        request: FastAPI request object
    """
    try:
        # Initialize services
        client_identification_service = get_client_identification_service()
        client_config_service = get_client_config_service()

        # Identify client from request
        client_id, client_schema = (
            await client_identification_service.identify_client_from_request(request)
        )

        if client_id:
            # Validate client access
            has_access = await client_identification_service.validate_client_access(
                client_id, request
            )
            if not has_access:
                logging.warning(
                    f"Client {client_id} does not have access to requested resource"
                )
                client_id = None
                client_schema = None

        # Load client configuration (will use defaults if client not identified)
        if client_id:
            client_config = await client_config_service.get_client_configuration(
                client_id, client_schema
            )
            logging.info(f"Loaded configuration for client: {client_id}")
        else:
            # Use default configuration
            client_config = client_config_service.get_default_configuration()
            logging.debug("Using default configuration (no client identified)")

        # Store configuration in request state
        request.state.client_config = client_config
        request.state.client_id = client_id
        request.state.client_schema = client_schema or client_id

    except Exception as e:
        logging.error(f"Error loading client configuration: {e}")
        # Fallback to default configuration
        request.state.client_config = (
            get_client_config_service().get_default_configuration()
        )
        request.state.client_id = None
        request.state.client_schema = None
