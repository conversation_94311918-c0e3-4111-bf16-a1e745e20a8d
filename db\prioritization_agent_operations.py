from fastapi import status
from constants import (
    PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES,
    SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES,
    DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE,
    DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE,
    DISABLE_SHOTGUN_SCORE_DECLINE,
    DISABLE_REFERRAL_PAYER_DECLINE,
    DISABLE_REFERRAL_SOURCE_DECLINE,
    DISABLE_REFERRAL_TOTAL_SCORE_DECLINE,
    SHOTGUN_SCORE_THRESHOLD,
    REFERRAL_PAYER_SCORE_THRESHOLD,
    REFERRAL_SOURCE_SCORE_THRESHOLD,
    REFERRAL_TOTAL_SCORE_THRESHOLD,
)
from utils.db_utils import get_model
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select, update, Numeric, cast
from sqlalchemy.exc import SQLAlchemyError
from exceptions import CustomError
from typing import List, Dict, Any
from models import PatientFacilityReferralFlag
from datetime import datetime, timezone


async def get_flagged_referrals_by_decline_rules(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get referrals that meet any configured decline rule.

    Returns:
        List of flagged referrals.
    """
    try:
        PatientFacilityReferralFlagModel = await get_model(
            PatientFacilityReferralFlag, schema_name
        )

        mandatory_filters = [
            PatientFacilityReferralFlagModel.refer_dcsn_priority_json.is_not(None),
            PatientFacilityReferralFlagModel.ai_status == None,
        ]

        optional_filters = []

        # Distance thresholds (miles to km)
        patient_dist_km = PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES * 1.60934
        source_dist_km = SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES * 1.60934

        score_json = PatientFacilityReferralFlagModel.refer_dcsn_priority_json

        if DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE != "true":
            optional_filters.append(
                cast(
                    score_json["score"]["patient_to_facility_distance"][
                        "distance_km"
                    ].astext,
                    Numeric,
                )
                > patient_dist_km
            )

        if DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE != "true":
            optional_filters.append(
                cast(
                    score_json["score"]["source_to_facility_distance"][
                        "distance_km"
                    ].astext,
                    Numeric,
                )
                > source_dist_km
            )

        if DISABLE_SHOTGUN_SCORE_DECLINE != "true":
            optional_filters.append(
                cast(score_json["score"]["shotgun"]["score"].astext, Numeric)
                < SHOTGUN_SCORE_THRESHOLD
            )

        if DISABLE_REFERRAL_PAYER_DECLINE != "true":
            optional_filters.append(
                cast(score_json["score"]["referral_payer"]["score"].astext, Numeric)
                < REFERRAL_PAYER_SCORE_THRESHOLD
            )

        if DISABLE_REFERRAL_SOURCE_DECLINE != "true":
            optional_filters.append(
                cast(score_json["score"]["referral_sources"]["score"].astext, Numeric)
                < REFERRAL_SOURCE_SCORE_THRESHOLD
            )

        if DISABLE_REFERRAL_TOTAL_SCORE_DECLINE != "true":
            optional_filters.append(
                cast(score_json["total_score"].astext, Numeric)
                < REFERRAL_TOTAL_SCORE_THRESHOLD
            )

        if not optional_filters:
            logging.info("All decline factors are disabled — skipping pending decline.")
            return []

        combined_filter = and_(*mandatory_filters, or_(*optional_filters))

        query = select(
            PatientFacilityReferralFlagModel.refer_id,
            PatientFacilityReferralFlagModel.refer_dcsn_id,
            PatientFacilityReferralFlagModel.refer_dcsn_priority_json,
            PatientFacilityReferralFlagModel.ai_status,
            PatientFacilityReferralFlagModel.ai_status_reason,
            PatientFacilityReferralFlagModel.ai_last_modified_at,
        ).where(combined_filter)

        result = await db.execute(query)
        rows = result.fetchall()
        columns = result.keys()

        flagged_referrals = [dict(zip(columns, row)) for row in rows]

        logging.info(
            f"Found {len(flagged_referrals)} referrals flagged by auto-decline rules."
        )

        return flagged_referrals

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def build_decline_reasons_json(referral_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    Build a structured JSON response of decline reasons for a single referral.

    Args:
        referral_json (Dict[str, Any]): The 'refer_dcsn_priority_json' dict from a referral.

    Returns:
        Dict[str, Any]: A structured JSON object listing all matched decline criteria.
    """
    reasons = []

    score = referral_json.get("score", {})
    total_score = referral_json.get("total_score")

    # Patient to facility distance
    if DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE != "true":
        pdist = score.get("patient_to_facility_distance")
        if pdist:
            dist_km = pdist.get("distance_km")
            if (
                dist_km is not None
                and dist_km > PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES * 1.60934
            ):
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_PDIST",
                        "reasonObjKey": "scoring",
                        "scoring": {"patient_to_facility_distance": pdist},
                    }
                )

    # Source to facility distance
    if DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE != "true":
        sdist = score.get("source_to_facility_distance")
        if sdist:
            dist_km = sdist.get("distance_km")
            if (
                dist_km is not None
                and dist_km > SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES * 1.60934
            ):
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SDIST",
                        "reasonObjKey": "scoring",
                        "scoring": {"source_to_facility_distance": sdist},
                    }
                )

    # Shotgun score
    if DISABLE_SHOTGUN_SCORE_DECLINE != "true":
        shotgun = score.get("shotgun")
        if shotgun and shotgun.get("score") is not None:
            if shotgun["score"] < SHOTGUN_SCORE_THRESHOLD:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SHOTGUN",
                        "reasonObjKey": "scoring",
                        "scoring": {"shotgun": shotgun},
                    }
                )

    # Referral payer score
    if DISABLE_REFERRAL_PAYER_DECLINE != "true":
        payer = score.get("referral_payer")
        if payer and payer.get("score") is not None:
            if payer["score"] < REFERRAL_PAYER_SCORE_THRESHOLD:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_PAYER",
                        "reasonObjKey": "scoring",
                        "scoring": {"referral_payer": payer},
                    }
                )

    # Referral source score
    if DISABLE_REFERRAL_SOURCE_DECLINE != "true":
        source = score.get("referral_sources")
        if source and source.get("score") is not None:
            if source["score"] < REFERRAL_SOURCE_SCORE_THRESHOLD:
                reasons.append(
                    {
                        "reasonCode": "SYSDEC_SOURCE",
                        "reasonObjKey": "scoring",
                        "scoring": {"referral_sources": source},
                    }
                )

    # Total referral score
    if DISABLE_REFERRAL_TOTAL_SCORE_DECLINE != "true":
        if total_score is not None and total_score < REFERRAL_TOTAL_SCORE_THRESHOLD:
            reasons.append(
                {
                    "reasonCode": "SYSDEC_TOTAL_SCORE",
                    "reasonObjKey": "scoring",
                    "scoring": {"total_score": total_score},
                }
            )

    return {"reasons": reasons}


async def update_referral_status_based_on_scores_and_distance(
    referral: Dict[str, Any], schema_name: str, db: AsyncSession
) -> Dict[str, Any]:
    """
    Update

    Args:
        referral: The referral to update.
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    reasons = build_decline_reasons_json(referral["refer_dcsn_priority_json"])
    refer_id = referral["refer_id"]
    refer_dcsn_id = referral["refer_dcsn_id"]

    try:
        PatientFacilityReferralFlagModel = await get_model(
            PatientFacilityReferralFlag, schema_name
        )

        stmt = (
            update(PatientFacilityReferralFlagModel)
            .where(
                and_(
                    PatientFacilityReferralFlagModel.refer_id == refer_id,
                    PatientFacilityReferralFlagModel.refer_dcsn_id == refer_dcsn_id,
                )
            )
            .values(
                ai_status="Pending_Decline",
                ai_status_reason=reasons,
                ai_last_modified_at=datetime.now(timezone.utc),
            )
            .execution_options(synchronize_session=False)
        )

        result = await db.execute(stmt)
        await db.commit()

        if result.rowcount > 0:
            logging.info(
                f"Updated ai_status to for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}"
            )
            return {
                "refer_id": refer_id,
                "refer_dcsn_id": refer_dcsn_id,
                "status": "SUCCESS",
                "message": f"Updated ai_status for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}",
            }
        else:
            return {
                "refer_id": refer_id,
                "refer_dcsn_id": refer_dcsn_id,
                "status": "NO_UPDATE",
                "message": f"No rows updated for refer_id: {refer_id}, refer_dcsn_id: {refer_dcsn_id}",
            }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
