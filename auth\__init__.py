from fastapi import Depends, HTTPException, status
from fastapi.security import (
    HTTPBasic,
    HTTPBasicCredentials,
    OAuth2PasswordRequestForm,
    OAuth2PasswordBearer,
)
from datetime import datetime, timedelta, timezone
import jwt
import bcrypt
from constants import (
    JWT_SECRET_KEY,
    ENCRYPTION_ALGORITHM,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_MINUTES,
    AUTH_CREDENTIALS,
    AUTH_KEYS_DELIMITER,
    ROOT_PATH,
    SWAGGER_BASIC_AUTH_KEY,
)
from exceptions import UnauthorizedError
import base64
import secrets
import logging


security = HTTPBasic()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{ROOT_PATH}/v1/token")


__valid_user_credentials = {}


async def __get_valid_user_credentials():
    """
    Get the valid user credentials from the environment variable.
    """
    global __valid_user_credentials
    if __valid_user_credentials:
        return __valid_user_credentials

    __valid_user_credentials = {
        user_pass.split(":")[0]: user_pass.split(":")[1]
        for user_pass in AUTH_CREDENTIALS.split(AUTH_KEYS_DELIMITER)
    }
    return __valid_user_credentials


async def verify_credentials(auth_form: OAuth2PasswordRequestForm):
    valid_user_credentials = await __get_valid_user_credentials()
    if auth_form.username in valid_user_credentials and bcrypt.checkpw(
        auth_form.password.encode(), valid_user_credentials[auth_form.username].encode()
    ):
        return

    raise UnauthorizedError("Invalid credentials!")


async def verify_user(username: str):
    if not username:
        raise UnauthorizedError("Invalid user!")

    valid_user_credentials = await __get_valid_user_credentials()
    if username in valid_user_credentials:
        return

    raise UnauthorizedError("Invalid user!")


async def get_auth_token(data: dict, client_id: str = None, client_schema: str = None):
    access_token = await create_access_token(data, client_id, client_schema)
    refresh_token = await create_refresh_token(data, client_id, client_schema)
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": refresh_token,
    }


async def verify_refresh_token(token: str, grand_type) -> dict:
    if grand_type != "refresh_token":
        raise UnauthorizedError("Invalid grand type")

    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ENCRYPTION_ALGORITHM])
        if payload.get("type") != "refresh":
            raise UnauthorizedError("Invalid token type")
        return payload
    except jwt.ExpiredSignatureError:
        raise UnauthorizedError("Refresh token has expired")
    except jwt.PyJWTError:
        raise UnauthorizedError("Could not validate refresh token")


async def create_access_token(
    data: dict, client_id: str = None, client_schema: str = None
) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire.timestamp(), "type": "access"})

    # Add client information to token if provided
    if client_id:
        to_encode["client_id"] = client_id
    if client_schema:
        to_encode["client_schema"] = client_schema

    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ENCRYPTION_ALGORITHM)


async def create_refresh_token(
    data: dict, client_id: str = None, client_schema: str = None
) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(
        minutes=REFRESH_TOKEN_EXPIRE_MINUTES
    )
    to_encode.update({"exp": expire.timestamp(), "type": "refresh"})

    # Add client information to token if provided
    if client_id:
        to_encode["client_id"] = client_id
    if client_schema:
        to_encode["client_schema"] = client_schema

    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ENCRYPTION_ALGORITHM)


async def verify_token(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ENCRYPTION_ALGORITHM])
        token_type: str = payload.get("type")
        if token_type != "access":
            raise UnauthorizedError("Invalid token type: must be access token")

        username = payload.get("user")
        await verify_user(username)
        return username
    except jwt.ExpiredSignatureError:
        raise UnauthorizedError("Token has expired")
    except jwt.InvalidTokenError:
        raise UnauthorizedError("Invalid token")


def authenticate_swagger_calls(credentials: HTTPBasicCredentials = Depends(security)):
    auth_value = f"{base64.b64encode(f'{credentials.username}:{credentials.password}'.encode()).decode()}"
    if not secrets.compare_digest(auth_value, SWAGGER_BASIC_AUTH_KEY):
        logging.error(f"Not authenticated! {credentials.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated!",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username
