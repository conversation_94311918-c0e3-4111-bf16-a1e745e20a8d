import os

ROOT_PATH = os.getenv("ROOT_PATH", "/referral-ai")
SERVER_NAME = os.getenv("SERVER_NAME", "localhost")

# Authentication
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "Linc<PERSON>areSecretKey")
ENCRYPTION_ALGORITHM = os.getenv("ENCRYPTION_ALGORITHM", "HS512")
ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "180")
)  # 3 hours
REFRESH_TOKEN_EXPIRE_MINUTES = int(
    os.getenv("REFRESH_TOKEN_EXPIRE_MINUTES", "10080")
)  # 7 days
AUTH_CREDENTIALS = os.getenv(
    "AUTH_CREDENTIALS",
    "admin:$2b$12$qFCvw6ET/Z3icYB4P/498eO/wjkaJ0wER.FFZp/MZZOHyeiOIw8Ma",
)
AUTH_KEYS_DELIMITER = os.getenv("AUTH_KEYS_DELIMITER", "||")
SWAGGER_BASIC_AUTH_KEY = os.getenv("SWAGGER_BASIC_AUTH_KEY", "YWRtaW46bGluY2RvYzE=")

# Database Connection
DB_HOST = os.getenv("DB_HOST", "127.0.0.1")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "lincdoc")
DB_USER = os.getenv("DB_USER", "lincdoc")
DB_PASS = os.getenv("DB_PASS", "password")
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "5"))
MAX_OVERFLOW_CONNECTIONS = int(os.getenv("MAX_OVERFLOW_CONNECTIONS", "5"))
POOL_TIMEOUT = int(os.getenv("POOL_TIMEOUT", "60"))

# Workers
CLIENT_WORKERS = int(os.getenv("CLIENT_WORKERS", "2"))
REFERRAL_PROCESSING_WORKERS = int(os.getenv("REFERRAL_PROCESSING_WORKERS", "10"))

# PDFPlus
PDFPLUS_URL = os.getenv(
    "PDFPLUS_URL",
    "https://5cziraruerxnwa7jwnjj7sblda0rwvxo.lambda-url.us-east-1.on.aws",
)
PDFPLUS_EXTRACT_FIELDS_ENDPOINT = os.getenv(
    "PDFPLUS_EXTRACT_FIELDS_ENDPOINT",
    "extract-pdf-fields",
)
PDFPLUS_AUTH_KEY = os.getenv("PDFPLUS_AUTH_KEY")
PDFPLUS_CONNECTION_TIMEOUT = int(os.getenv("PDFPLUS_CONNECTION_TIMEOUT", "30"))
PDFPLUS_READ_TIMEOUT = int(os.getenv("PDFPLUS_READ_TIMEOUT", "120"))

# Client Configurations (Missing Info Agent)
DISABLE_MISSING_INFO_AGENT = os.getenv("DISABLE_MISSING_INFO_AGENT", "true").lower()
DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW = os.getenv(
    "DISABLE_MISSING_INFO_NEW_REFERRAL_WORKFLOW", "true"
).lower()
DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW = os.getenv(
    "DISABLE_MISSING_INFO_RETRIGGER_WORKFLOW", "true"
).lower()
MISSING_FIELDS_TO_LOOK = os.getenv(
    "MISSING_FIELDS_TO_LOOK", "res_ssn||resmce_id||res_acsz"
).split("||")
FIELDS_MAPPING_PROMPT = os.getenv(
    "FIELDS_MAPPING_PROMPT",
    "SSN||Medicare HIC Number (MBI) 11 characters (digit, letter, alphanumeric, digit, letter, alphanumeric, digit, two letters, two digits)||Patient Address with City, State, Zip",
).split("||")
MISSING_FIELDS_LABELS = os.getenv(
    "MISSING_FIELDS_LABELS",
    "SSN||Medicare ID||Patient Address",
).split("||")
HIGH_PRIORITY_FIELDS = [
    field.strip()
    for field in os.getenv(
        "HIGH_PRIORITY_FIELDS",
        "res_ssn||resmce_id",
    ).split("||")
]

# Client Configurations (Task Workflow Agent)
TASK_SUBJECT = os.getenv("TASK_SUBJECT", "Missing Information")
NEW_COMMENT_TASK_STATUS_FOR_MISSING_FIELDS = os.getenv("STATUS", "New Request")
COMMENT = os.getenv("COMMENT", "Please get the {data_point} for this patient referral.")
COMMENT_TYPE = os.getenv("COMMENT_TYPE", "General")
CREATED_BY_USERNAME = os.getenv("CREATED_BY_USERNAME", "ReferralAI")
CREATED_BY_USER_ID = int(os.getenv("CREATED_BY_USER_ID", "-3"))
TASK_CREATION_WAITING_MINUTES = int(os.getenv("TASK_CREATION_WAITING_MINUTES", "300"))
DISABLE_TASK_CREATION_WORKFLOW = os.getenv(
    "DISABLE_TASK_CREATION_WORKFLOW", "true"
).lower()
DISABLE_TASK_AND_WORKFLOW_AGENT = os.getenv(
    "DISABLE_TASK_AND_WORKFLOW_AGENT", "true"
).lower()

# Client Configurations (Prioritization Agent)
DISABLE_PRIORITIZATION_AGENT = os.getenv("DISABLE_PRIORITIZATION_AGENT", "true").lower()
DISABLE_AUTO_DECLINE_WORKFLOW = os.getenv(
    "DISABLE_AUTO_DECLINE_WORKFLOW", "true"
).lower()
DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE = os.getenv(
    "DISABLE_PATIENT_TO_FACILITY_DISTANCE_DECLINE", "true"
).lower()
DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE = os.getenv(
    "DISABLE_SOURCE_TO_FACILITY_DISTANCE_DECLINE", "true"
).lower()
DISABLE_SHOTGUN_SCORE_DECLINE = os.getenv(
    "DISABLE_SHOTGUN_SCORE_DECLINE", "true"
).lower()
DISABLE_REFERRAL_PAYER_DECLINE = os.getenv(
    "DISABLE_REFERRAL_PAYER_DECLINE", "true"
).lower()
DISABLE_REFERRAL_SOURCE_DECLINE = os.getenv(
    "DISABLE_REFERRAL_SOURCE_DECLINE", "true"
).lower()
DISABLE_REFERRAL_TOTAL_SCORE_DECLINE = os.getenv(
    "DISABLE_REFERRAL_TOTAL_SCORE_DECLINE", "true"
).lower()

SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES = float(
    os.getenv("SOURCE_TO_FACILITY_DISTANCE_THRESHOLD_MILES", "100")
)
PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES = float(
    os.getenv("PATIENT_TO_FACILITY_DISTANCE_THRESHOLD_MILES", "100")
)
SHOTGUN_SCORE_THRESHOLD = float(os.getenv("SHOTGUN_SCORE_THRESHOLD", "20"))
REFERRAL_PAYER_SCORE_THRESHOLD = float(
    os.getenv("REFERRAL_PAYER_SCORE_THRESHOLD", "20")
)
REFERRAL_SOURCE_SCORE_THRESHOLD = float(
    os.getenv("REFERRAL_SOURCE_SCORE_THRESHOLD", "20")
)
REFERRAL_TOTAL_SCORE_THRESHOLD = float(
    os.getenv("REFERRAL_TOTAL_SCORE_THRESHOLD", "20")
)

# Client Configurations (Error Monitoring Service)
DISABLE_ERROR_MONITORING = os.getenv("DISABLE_ERROR_MONITORING", "false").lower()
ERROR_ALERT_INTERVAL_MINUTES = int(os.getenv("ERROR_ALERT_INTERVAL_MINUTES", "1440"))
ERROR_ALERT_SUBJECT = os.getenv(
    "ERROR_ALERT_SUBJECT", f"🚨 Error alert: Log monitoring {SERVER_NAME}!"
)
EMAIL_TO_USER = os.getenv("EMAIL_TO_USER")
EMAIL_FROM_USER = os.getenv("EMAIL_FROM_USER")
ENABLE_POSTFIX_EMAIL = os.getenv("ENABLE_POSTFIX_EMAIL", "false").lower()
EMAIL_AUTH_USER = os.getenv("EMAIL_AUTH_USER", "test")
EMAIL_APP_PASSWORD = os.getenv("EMAIL_APP_PASSWORD", "test")

# Scheduler
DISABLE_INTERNAL_SCHEDULER = os.getenv("DISABLE_INTERNAL_SCHEDULER", "false").lower()
ERROR_MONITORING_INTERVAL_SECONDS = int(
    os.getenv("ERROR_MONITORING_INTERVAL_SECONDS", "300")
)
MISSING_INFO_AGENT_INTERVAL_SECONDS = int(
    os.getenv("MISSING_INFO_AGENT_INTERVAL_SECONDS", "600")
)
TASK_WORKFLOW_AGENT_INTERVAL_SECONDS = int(
    os.getenv("TASK_WORKFLOW_AGENT_INTERVAL_SECONDS", "600")
)
PRIORITIZATION_AGENT_INTERVAL_SECONDS = int(
    os.getenv("PRIORITIZATION_AGENT_INTERVAL_SECONDS", "600")
)
