import asyncio
import logging
from datetime import datetime, timezone
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, START, END
from constants import DISABLE_TASK_AND_WORKFLOW_AGENT, DISABLE_TASK_CREATION_WORKFLOW
from db import get_db
from db.monitoring import update_agent_run_timestamp
from db.missing_info_agent_operations import get_clients
from db.task_and_workflow_agent_operations import (
    get_referrals_with_pending_tasks,
    create_tasks_for_missing_fields,
    get_referrals_with_auto_complete_tasks,
    mark_tasks_as_completed,
)
from utils.logging_config import REQUEST_ID_VAR


class TaskManagementAndWorkflowAgentState(TypedDict):
    """Represents the data flowing through our pipeline."""

    agent_summary: List[Dict[str, Any]]
    task_list: List[Dict[str, Any]]
    auto_complete_task_list: List[Dict[str, Any]]
    auto_complete_summary: List[Dict[str, Any]]
    client_schema: str
    request_id: str


class TaskManagementAndWorkflowAgent:

    def __init__(self):
        self.workflow_enabled = False
        self.graph = StateGraph(TaskManagementAndWorkflowAgentState)
        self.create_task_creation_workflow()

    def create_task_creation_workflow(self):
        """Creates the task creation workflow."""
        if DISABLE_TASK_CREATION_WORKFLOW == "true":
            logging.info("Task creation workflow is disabled. Skipping it...")
            return

        self.graph.add_node(
            "Identify Tasks to create",
            self.identify_tasks_to_create,
        )
        self.graph.add_node(
            "Create Tasks for Missing Fields", self.create_tasks_for_missing_fields
        )
        self.graph.add_node(
            "Identify Tasks to Auto-Complete", self.identify_tasks_to_auto_complete
        )
        self.graph.add_node("Auto-Complete Tasks", self.auto_complete_tasks)
        self.graph.add_node("Summarize the Agent Actions", self.summarize_agent_actions)

        self.graph.add_edge(START, "Identify Tasks to create")
        self.graph.add_edge(
            "Identify Tasks to create", "Create Tasks for Missing Fields"
        )
        self.graph.add_edge(
            "Create Tasks for Missing Fields",
            "Identify Tasks to Auto-Complete",
        )
        self.graph.add_edge(
            "Identify Tasks to Auto-Complete",
            "Auto-Complete Tasks",
        )
        self.graph.add_edge(
            "Auto-Complete Tasks",
            "Summarize the Agent Actions",
        )
        self.graph.add_edge("Summarize the Agent Actions", END)
        self.workflow_enabled = True

    async def execute_workflow(self, state: TaskManagementAndWorkflowAgentState):
        """Executes the workflow for a given client schema."""
        return await self.graph.compile(debug=False).ainvoke(state)

    async def run(self, clients: List[str]):
        logging.info(f"Identified {len(clients)} client(s).")
        tasks = []
        for client in clients:
            state = TaskManagementAndWorkflowAgentState(
                agent_summary=[],
                task_list=[],
                auto_complete_task_list=[],
                auto_complete_summary=[],
                client_schema=f"{client}_referral",
                request_id=REQUEST_ID_VAR.get(),
            )
            tasks.append(self.execute_workflow(state))

        await asyncio.gather(*tasks)
        logging.info("Task Management Workflow completed.")

    async def identify_tasks_to_create(
        self, state: TaskManagementAndWorkflowAgentState
    ) -> TaskManagementAndWorkflowAgentState:
        """
        Query the database for referrals missing fields. Store those in state["patient_referrals"].
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the missing referrals.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Querying PostgreSQL for pending tasks to create..")
        async with get_db() as db:
            pending_tasks_records = await get_referrals_with_pending_tasks(
                state["client_schema"], db
            )
        return {"task_list": pending_tasks_records}

    async def create_tasks_for_missing_fields(
        self, state: TaskManagementAndWorkflowAgentState
    ):
        """
        Creates tasks for the missing fields identified in the missing info process.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Creating tasks for missing fields.")
        task_list = state.get("task_list", [])
        if not task_list:
            logging.info("No tasks to create. Skipping task creation.")
            return
        async with get_db() as db:
            agent_summary = await create_tasks_for_missing_fields(
                task_list=task_list, client_schema=state["client_schema"], db=db
            )
            await db.commit()
        return {"agent_summary": agent_summary}

    async def identify_tasks_to_auto_complete(
        self, state: TaskManagementAndWorkflowAgentState
    ) -> TaskManagementAndWorkflowAgentState:
        """
        Query the database for referrals where require_task is False and task_created is True.
        These tasks should be auto-completed.

        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the tasks to auto-complete.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Querying PostgreSQL for tasks to auto-complete...")
        async with get_db() as db:
            auto_complete_task_list = await get_referrals_with_auto_complete_tasks(
                state["client_schema"], db
            )
        return {"auto_complete_task_list": auto_complete_task_list}

    async def auto_complete_tasks(
        self, state: TaskManagementAndWorkflowAgentState
    ) -> TaskManagementAndWorkflowAgentState:
        """
        Mark tasks as complete for referrals where require_task is False and task_created is True.

        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the auto-complete summary.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Auto-completing tasks...")
        auto_complete_task_list = state.get("auto_complete_task_list", [])
        if not auto_complete_task_list:
            logging.info("No tasks to auto-complete. Skipping.")
            return {"auto_complete_summary": []}

        auto_complete_summary = []
        async with get_db() as db:
            for task in auto_complete_task_list:
                refer_id = task["refer_id"]
                task_completion_result = await mark_tasks_as_completed(
                    refer_id=refer_id, schema_name=state["client_schema"], db=db
                )
                auto_complete_summary.append(task_completion_result)

        return {"auto_complete_summary": auto_complete_summary}

    async def summarize_agent_actions(
        self, state: TaskManagementAndWorkflowAgentState
    ) -> str:
        """
        End node: returns a textual summary of what happened.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Summarizing the Agent actions.")
        summary_lines = [
            "\n=== Task Creation Summary ===",
            f"Pending Tasks: {len(state.get('task_list', []))}",
            f"Created Tasks: {len(state.get('agent_summary', []))}",
            f"Auto-Completed Tasks: {len(state.get('auto_complete_summary', []))}",
        ]

        summary_lines.append("Task Creation Results:")
        for agent_summary in state.get("agent_summary", []):
            summary_lines.append(
                f" - refer_id={agent_summary['refer_id']} status={agent_summary['status']} msg={agent_summary['message']}"
            )

        if state.get("auto_complete_summary", []):
            summary_lines.append("\nAuto-Complete Results:")
            for auto_complete in state.get("auto_complete_summary", []):
                summary_lines.append(
                    f" - refer_id={auto_complete['refer_id']} status={auto_complete['status']}"
                )

        summary_lines.append("=======================================")
        summary_lines = "\n".join(summary_lines)
        logging.info(summary_lines)

    async def start(self, user="Scheduler"):
        logging.info(f"Task Management and Workflow Agent started by {user}.")
        if DISABLE_TASK_AND_WORKFLOW_AGENT == "true":
            return {
                "message": "Task and Workflow agent is disabled. Please enable it to run."
            }
        if not self.workflow_enabled:
            logging.info(
                "No workflow is enabled for task and workflow agent. Skipping."
            )
            return {
                "message": "No workflow is enabled for task and workflow agent to run."
            }
        clients = await get_clients()
        current_time = datetime.now(timezone.utc)
        await self.run(clients)
        async with get_db() as db:
            for client in clients:
                schema_name = f"{client}_referral"
                await update_agent_run_timestamp(
                    db=db,
                    agent_name="Task Creation Agent",
                    schema_name=schema_name,
                    run_timestamp=current_time,
                )

        return {
            "message": f"Task Management and Workflow Agent completed successfully by {user}."
        }
